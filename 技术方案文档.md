# 南京市政策爬虫技术方案文档

## 1. 项目概述

### 1.1 目标网站
- 目标URL: https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html
- 网站类型: 南京市政府政策发布平台

### 1.2 爬取要求
- **发布层级**: 市
- **发布机构**: 市发改委、市科技局、市工信局
- **数据字段**: title、create_time、address、content、fbjg、link

## 2. 技术架构分析

### 2.1 网站结构分析
根据提供的页面截图分析：

1. **列表页面结构**:
   - 主页面包含政策列表
   - 每条政策都有标题和发布时间
   - 需要点击进入详情页获取完整信息

2. **详情页面结构**:
   - 包含完整的政策标题
   - 发布机构信息
   - 发布时间
   - 政策正文内容
   - 可能包含附件下载链接

3. **交互特点**:
   - 发布时间筛选需要通过点击事件触发
   - 需要逐个点击政策标题进入详情页
   - 可能存在分页机制

### 2.2 技术选型
- **主要工具**: Selenium WebDriver
- **浏览器**: Chrome + ChromeDriver
- **编程语言**: Python
- **数据存储**: JSON/CSV格式

## 3. 实现方案

### 3.1 核心流程设计

```
1. 初始化Selenium WebDriver
2. 访问目标页面
3. 设置筛选条件（发布机构（发布层级中））
4. 获取政策列表
5. 逐个访问政策详情页
6. 提取所需数据字段
7. 保存数据到文件
8. 处理分页（如果存在）
9. 清理资源
```

### 3.2 关键技术点

#### 3.2.1 发布机构筛选
- 筛选条件：市发改委、市科技局、市工信局
- 需要找到对应的筛选选项并进行选择

#### 3.2.2 数据提取策略
- **title**: 从详情页面的标题元素提取
- **address**: 当前详情页的URL
- **content**: 提取政策正文，去除HTML标签，只保留文字
- **fbjg**: 从页面的发布机构字段提取
- **link**: 查找页面中的附件下载链接

#### 3.2.3 反爬虫对策
- 设置合理的请求间隔
- 使用随机User-Agent
- 处理可能的验证码或反爬虫机制

### 3.3 异常处理
- 网络超时处理
- 页面加载失败处理
- 元素定位失败处理
- 数据格式异常处理

## 4. 数据结构设计

### 4.1 输出数据格式
```json
{
  "title": "政策标题",
  "create_time": "政策发布时间",
  "address": "详情页URL",
  "content": "政策正文内容（纯文字）",
  "fbjg": "发布机构",
  "link": ["附件链接1", "附件链接2"]
}
```

### 4.2 存储方案
- 主要格式：JSON文件
- 备选格式：CSV文件（便于Excel查看）
- 文件命名：包含爬取时间戳


人工筛选逻辑：
人为的筛选逻辑如下：
1.打开网页
2.发布层级有俩行，第一行选：市，在第一行选择后会显示第二行，点击展开，再点击发布机构：市发改委，市科技局，市工信局（每次只能筛选一个）
3.得到符合要求的政策链接

- 人工筛选结果如下：
      市发改委：15条
      市科技局：26条
      市工信局：7条


